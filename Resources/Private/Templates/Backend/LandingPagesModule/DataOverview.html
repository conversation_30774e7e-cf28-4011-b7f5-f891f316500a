<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:be="http://typo3.org/ns/TYPO3/CMS/Backend/ViewHelpers"
      data-namespace-typo3-fluid="true">

<f:layout name="Module" />

<f:section name="Content">
    <div class="landing-pages-module">
        <!-- Description -->
        <div class="alert alert-info">
            <strong>Reference Data Overview</strong><br>
            This section shows statistics and management options for the reference data tables used by the Flight Landing Pages extension.
        </div>

        <f:if condition="{dataStatistics.error}">
            <f:then>
                <div class="alert alert-danger">
                    <strong>Error:</strong> {dataStatistics.error}
                </div>
            </f:then>
            <f:else>
                <!-- Data Tables Cards -->
                <div class="data-tables-list">
                    <f:for each="{dataStatistics}" as="tableData" key="tableName">
                        <div class="panel panel-default data-table-card">
                            <div class="panel-body">
                                <!-- Main Content Row -->
                                <div class="row">
                                    <!-- Table Info -->
                                    <div class="col-md-8">
                                        <h4 class="table-title">
                                            <be:icon identifier="{tableData.icon}" size="small" />
                                            {tableData.title}
                                        </h4>
                                        <p class="text-muted">{tableData.description}</p>
                                    </div>

                                    <!-- Statistics -->
                                    <div class="col-md-4">
                                        <div class="data-stat">
                                            <h4 class="text-primary">{tableData.totalRecords}</h4>
                                            <small class="text-muted">Total Records</small>
                                        </div>

                                        <!-- Translations -->
                                        <f:if condition="{tableData.translations}">
                                            <div class="translations-section">
                                                <small class="text-muted">Translations:</small>
                                                <div class="translation-stats">
                                                    <f:for each="{tableData.translations}" as="translation">
                                                        <span class="badge badge-info translation-badge">
                                                            {translation.lang}: {translation.count}
                                                        </span>
                                                    </f:for>
                                                </div>
                                            </div>
                                        </f:if>
                                    </div>
                                </div>

                                <!-- Action Buttons Row -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="action-buttons">
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-default" disabled>
                                                    <be:icon identifier="actions-download" size="small" />
                                                    Import
                                                </button>
                                                <button type="button" class="btn btn-default" disabled>
                                                    <be:icon identifier="actions-upload" size="small" />
                                                    Export
                                                </button>
                                                <button type="button" class="btn btn-default" disabled>
                                                    <be:icon identifier="actions-list" size="small" />
                                                    List
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </f:for>
                </div>


            </f:else>
        </f:if>
    </div>

    <style>
        .data-tables-list {
            margin-top: 20px;
        }

        .data-table-card {
            margin-bottom: 15px;
        }

        .data-table-card .panel-body {
            padding-bottom: 10px;
        }

        .table-title {
            margin: 0 0 10px 0;
            color: #333;
            display: flex;
            align-items: center;
        }

        .table-title .t3js-icon {
            margin-right: 8px;
        }

        .data-stat {
            text-align: center;
            margin-bottom: 15px;
        }

        .data-stat h4 {
            margin: 0;
            font-size: 2.2em;
            font-weight: bold;
        }

        .translations-section {
            margin-top: 10px;
        }

        .translation-badge {
            margin: 2px;
            font-size: 0.75em;
        }

        .action-buttons {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
        }

        .btn-group .btn {
            margin-right: 5px;
        }

        .btn-group .btn:last-child {
            margin-right: 0;
        }

        .btn .t3js-icon {
            margin-right: 5px;
        }
    </style>
</f:section>

</html>
