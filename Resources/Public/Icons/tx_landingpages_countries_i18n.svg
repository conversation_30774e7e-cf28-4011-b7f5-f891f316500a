<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="256" height="256" viewBox="0 0 256 256">
    <rect x="0" y="0" width="256" height="256" fill="#9C27B0" rx="20" ry="20"/>
    <g transform="translate(128, 128)">
        <!-- Globe outline -->
        <circle cx="0" cy="0" r="60" fill="none" stroke="white" stroke-width="3" opacity="0.8"/>
        
        <!-- Simplified continents -->
        <g fill="white" opacity="0.6">
            <path d="M-15,-45 Q-5,-35 5,-25 Q15,-15 10,5 Q5,25 -5,35 Q-15,25 -20,5 Q-15,-15 -15,-35 Z"/>
            <path d="M-45,-25 Q-35,-15 -30,5 Q-35,25 -45,35 Q-55,25 -50,5 Q-55,-15 -45,-25 Z"/>
            <path d="M25,-35 Q40,-25 45,-5 Q40,15 25,25 Q15,15 20,-5 Q25,-25 25,-35 Z"/>
        </g>
        
        <!-- Language flags/symbols -->
        <g transform="translate(-70, 70)">
            <rect x="-15" y="-8" width="30" height="16" fill="white" opacity="0.9"/>
            <text x="0" y="4" font-family="Arial" font-size="12" fill="#9C27B0" text-anchor="middle">EN</text>
        </g>
        <g transform="translate(0, 85)">
            <rect x="-15" y="-8" width="30" height="16" fill="white" opacity="0.9"/>
            <text x="0" y="4" font-family="Arial" font-size="12" fill="#9C27B0" text-anchor="middle">DE</text>
        </g>
        <g transform="translate(70, 70)">
            <rect x="-15" y="-8" width="30" height="16" fill="white" opacity="0.9"/>
            <text x="0" y="4" font-family="Arial" font-size="12" fill="#9C27B0" text-anchor="middle">FR</text>
        </g>
    </g>
</svg>
