<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="256" height="256" viewBox="0 0 256 256">
    <rect x="0" y="0" width="256" height="256" fill="#FF9800" rx="20" ry="20"/>
    <g transform="translate(128, 128)">
        <!-- Globe outline -->
        <circle cx="0" cy="0" r="80" fill="none" stroke="white" stroke-width="4" opacity="0.9"/>
        
        <!-- Continents (simplified) -->
        <g fill="white" opacity="0.8">
            <!-- Europe/Africa -->
            <path d="M-20,-60 Q-10,-50 0,-40 Q10,-30 20,-20 Q15,0 10,20 Q0,40 -10,50 Q-20,40 -30,20 Q-25,0 -20,-20 Z"/>
            
            <!-- Americas -->
            <path d="M-60,-40 Q-50,-30 -45,-10 Q-40,10 -45,30 Q-50,50 -60,60 Q-70,50 -75,30 Q-80,10 -75,-10 Q-70,-30 -60,-40 Z"/>
            
            <!-- Asia -->
            <path d="M30,-50 Q50,-40 60,-20 Q70,0 65,20 Q60,40 50,50 Q40,40 35,20 Q30,0 35,-20 Q40,-40 30,-50 Z"/>
        </g>
        
        <!-- Grid lines -->
        <g stroke="white" stroke-width="2" opacity="0.6" fill="none">
            <line x1="-80" y1="0" x2="80" y2="0"/>
            <line x1="0" y1="-80" x2="0" y2="80"/>
            <ellipse cx="0" cy="0" rx="80" ry="40"/>
            <ellipse cx="0" cy="0" rx="40" ry="80"/>
        </g>
    </g>
</svg>
