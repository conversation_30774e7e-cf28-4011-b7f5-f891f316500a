<?php
return [
    'ctrl' => [
        'title' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_countries',
        'label' => 'name',
        'label_alt' => 'code',
        'label_alt_force' => true,
        'tstamp' => 'tstamp',
        'crdate' => 'crdate',
        'searchFields' => 'code,name,alt_name,phone_code',
        'iconfile' => 'EXT:landing-pages/Resources/Public/Icons/tx_landingpages_countries.svg',
        'rootLevel' => 1,
        'security' => [
            'ignorePageTypeRestriction' => true,
        ],
    ],
    'types' => [
        '1' => [
            'showitem' => '
                --palette--;;basic_info,
                --palette--;;additional_info
            '
        ]
    ],
    'palettes' => [
        'basic_info' => [
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_countries.palette.basic_info',
            'showitem' => 'code, name, --linebreak--, alt_name',
        ],
        'additional_info' => [
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_countries.palette.additional_info',
            'showitem' => 'phone_code',
        ],
    ],
    'columns' => [
        'code' => [
            'exclude' => false,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_countries.code',
            'config' => [
                'type' => 'input',
                'size' => 5,
                'max' => 2,
                'eval' => 'trim,upper,required',
                'placeholder' => 'DE',
            ],
        ],
        'name' => [
            'exclude' => false,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_countries.name',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'max' => 200,
                'eval' => 'trim,required',
                'placeholder' => 'Germany',
            ],
        ],
        'alt_name' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_countries.alt_name',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'max' => 255,
                'eval' => 'trim',
                'placeholder' => 'Deutschland',
            ],
        ],
        'phone_code' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_countries.phone_code',
            'config' => [
                'type' => 'input',
                'size' => 10,
                'max' => 6,
                'eval' => 'trim',
                'placeholder' => '+49',
            ],
        ],
    ],
];
