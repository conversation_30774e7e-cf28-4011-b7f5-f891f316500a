# Reference Data Tables Implementation

## Overview

This document describes the implementation of reference data tables for the Flight Landing Pages extension. These tables store airport, country, and city data that can be used throughout the extension for flight route management and content generation.

## Implemented Tables

### 1. Airports (`tx_landingpages_airports`)

**Purpose**: Store airport reference data including codes, names, and geographic information.

**Key Fields**:
- `code` (varchar(3)) - Primary key, IATA airport code (e.g., "BER")
- `name` (varchar(250)) - Full airport name (e.g., "Berlin Brandenburg Airport")
- `ident` (varchar(10)) - ICAO identifier (e.g., "EDDB")
- `lat`, `lon` (varchar(20)) - Geographic coordinates
- `country_code` (varchar(2)) - ISO country code
- `continent` (varchar(3)) - Continent code
- `city_id` (int(11)) - Reference to cities table

### 2. Airport Translations (`tx_landingpages_airports_i18n`)

**Purpose**: Store translated airport names for multilingual support.

**Key Fields**:
- `code` (varchar(3)) - Reference to airport code
- `lang` (varchar(5)) - Language code (e.g., "en", "de", "fr")
- `name` (varchar(250)) - Translated airport name

### 3. Countries (`tx_landingpages_countries`)

**Purpose**: Store country reference data.

**Key Fields**:
- `code` (varchar(2)) - Primary key, ISO country code (e.g., "DE")
- `name` (varchar(200)) - Country name (e.g., "Germany")
- `alt_name` (varchar(255)) - Alternative name (e.g., "Deutschland")
- `phone_code` (varchar(6)) - International phone code (e.g., "+49")

### 4. Country Translations (`tx_landingpages_countries_i18n`)

**Purpose**: Store translated country names for multilingual support.

**Key Fields**:
- `code` (varchar(2)) - Reference to country code
- `lang` (varchar(2)) - Language code
- `name` (varchar(200)) - Translated country name

### 5. Cities (`tx_landingpages_cities`)

**Purpose**: Store city reference data.

**Key Fields**:
- `id` (int(11)) - Primary key, unique city identifier
- `region_id` (int(11)) - Regional identifier
- `name` (varchar(250)) - City name (e.g., "Berlin")
- `alt_name` (varchar(255)) - Alternative name
- `ak` (int(11)) - AK sorting priority (0 = disabled, higher = more important)

### 6. City Translations (`tx_landingpages_cities_i18n`)

**Purpose**: Store translated city names for multilingual support.

**Key Fields**:
- `id` (int(11)) - Reference to city ID
- `lang` (varchar(2)) - Language code
- `name` (varchar(250)) - Translated city name

## Technical Implementation

### Database Schema

All tables follow TYPO3 v12.04 conventions:
- Table names prefixed with `tx_landingpages_`
- Standard TYPO3 fields: `tstamp`, `crdate`
- Proper indexing for performance
- No foreign key constraints (TYPO3 best practice)

### TCA Configuration

Each table has complete TCA configuration in `Configuration/TCA/`:
- `tx_landingpages_airports.php`
- `tx_landingpages_airports_i18n.php`
- `tx_landingpages_countries.php`
- `tx_landingpages_countries_i18n.php`
- `tx_landingpages_cities.php`
- `tx_landingpages_cities_i18n.php`

### Backend Interface

- All tables are allowed on standard pages
- Organized field layouts with palettes
- Proper form validation and required fields
- Search functionality enabled
- Custom SVG icons for each table type

### Language Support

- Complete language labels in `locallang_db.xlf`
- Multi-language dropdown for translation tables
- Support for major languages (EN, DE, FR, ES, IT, PT, NL, RU, ZH, JA)

### Icons

Custom SVG icons created for each table:
- Airports: Green background with airplane and runway
- Countries: Orange background with globe and continents
- Cities: Blue-grey background with city skyline
- Translation tables: Similar designs with language indicators

## Usage

These reference data tables can be used for:

1. **Flight Route Validation**: Validate origin/destination codes against airport data
2. **Content Generation**: Generate dynamic content using real airport/city/country names
3. **Multilingual Support**: Display localized names based on site language
4. **Data Import**: Import flight data from external APIs using standardized codes
5. **Search and Filtering**: Enable advanced search by location, country, or region

## Next Steps

1. **Data Import**: Create import functionality for populating tables with real data
2. **API Integration**: Connect to external flight data APIs using these reference codes
3. **Frontend Display**: Create frontend components that utilize this reference data
4. **Validation**: Add validation to flight routes using this reference data
5. **Search Enhancement**: Implement autocomplete and search features using this data

## Files Modified/Created

### Database Schema
- `ext_tables.sql` - Added 6 new table definitions

### TCA Configuration
- `Configuration/TCA/tx_landingpages_airports.php`
- `Configuration/TCA/tx_landingpages_airports_i18n.php`
- `Configuration/TCA/tx_landingpages_countries.php`
- `Configuration/TCA/tx_landingpages_countries_i18n.php`
- `Configuration/TCA/tx_landingpages_cities.php`
- `Configuration/TCA/tx_landingpages_cities_i18n.php`

### Extension Configuration
- `ext_tables.php` - Registered new tables
- `Resources/Private/Language/locallang_db.xlf` - Added language labels

### Icons
- `Resources/Public/Icons/tx_landingpages_airports.svg`
- `Resources/Public/Icons/tx_landingpages_airports_i18n.svg`
- `Resources/Public/Icons/tx_landingpages_countries.svg`
- `Resources/Public/Icons/tx_landingpages_countries_i18n.svg`
- `Resources/Public/Icons/tx_landingpages_cities.svg`
- `Resources/Public/Icons/tx_landingpages_cities_i18n.svg`

The implementation follows TYPO3 v12.04 best practices and provides a solid foundation for managing flight-related reference data within the extension.
