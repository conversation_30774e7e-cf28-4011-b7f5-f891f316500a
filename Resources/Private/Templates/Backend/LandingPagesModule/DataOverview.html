<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:be="http://typo3.org/ns/TYPO3/CMS/Backend/ViewHelpers"
      data-namespace-typo3-fluid="true">

<f:layout name="Module" />

<f:section name="Content">
    <div class="landing-pages-module">
        <!-- Description -->
        <div class="alert alert-info">
            <strong>Reference Data Overview</strong><br>
            This section shows statistics and management options for the reference data tables used by the Flight Landing Pages extension.
        </div>

        <f:if condition="{dataStatistics.error}">
            <f:then>
                <div class="alert alert-danger">
                    <strong>Error:</strong> {dataStatistics.error}
                </div>
            </f:then>
            <f:else>
                <!-- Data Tables Cards -->
                <div class="row">
                    <f:for each="{dataStatistics}" as="tableData" key="tableName">
                        <div class="col-md-4">
                            <div class="panel panel-default data-table-card">
                                <div class="panel-heading">
                                    <h3 class="panel-title">
                                        <i class="fa fa-plane" aria-hidden="true"></i>
                                        {tableData.title}
                                    </h3>
                                </div>
                                <div class="panel-body">
                                    <!-- Record Count -->
                                    <div class="data-stat">
                                        <h4 class="text-primary">{tableData.totalRecords}</h4>
                                        <small class="text-muted">Total Records</small>
                                    </div>

                                    <!-- Description -->
                                    <p class="text-muted small">{tableData.description}</p>

                                    <!-- Translations -->
                                    <f:if condition="{tableData.translations}">
                                        <div class="translations-section">
                                            <h5>Translations by Language</h5>
                                            <div class="translation-stats">
                                                <f:for each="{tableData.translations}" as="translation">
                                                    <span class="badge badge-info translation-badge">
                                                        {translation.lang}: {translation.count}
                                                    </span>
                                                </f:for>
                                            </div>
                                        </div>
                                    </f:if>

                                    <!-- Action Buttons -->
                                    <div class="action-buttons">
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-default" disabled>
                                                <i class="fa fa-download" aria-hidden="true"></i>
                                                Import
                                            </button>
                                            <button type="button" class="btn btn-default" disabled>
                                                <i class="fa fa-upload" aria-hidden="true"></i>
                                                Export
                                            </button>
                                            <button type="button" class="btn btn-default" disabled>
                                                <i class="fa fa-list" aria-hidden="true"></i>
                                                List
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </f:for>
                </div>

                <!-- Summary Statistics -->
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">Summary Statistics</h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="stat-box">
                                    <h4 class="text-success">{dataStatistics.airports.totalRecords}</h4>
                                    <small>Airports</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-box">
                                    <h4 class="text-info">{dataStatistics.countries.totalRecords}</h4>
                                    <small>Countries</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-box">
                                    <h4 class="text-warning">{dataStatistics.cities.totalRecords}</h4>
                                    <small>Cities</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-box">
                                    <h4 class="text-primary">
                                        <f:variable name="totalTranslations" value="0" />
                                        <f:for each="{dataStatistics}" as="tableData">
                                            <f:for each="{tableData.translations}" as="translation">
                                                <f:variable name="totalTranslations" value="{totalTranslations + translation.count}" />
                                            </f:for>
                                        </f:for>
                                        {totalTranslations}
                                    </h4>
                                    <small>Total Translations</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </f:else>
        </f:if>
    </div>

    <style>
        .data-table-card {
            margin-bottom: 20px;
            min-height: 300px;
        }
        
        .data-stat {
            text-align: center;
            margin-bottom: 15px;
        }
        
        .data-stat h4 {
            margin: 0;
            font-size: 2.5em;
            font-weight: bold;
        }
        
        .translations-section {
            margin: 15px 0;
        }
        
        .translations-section h5 {
            font-size: 0.9em;
            margin-bottom: 8px;
            color: #666;
        }
        
        .translation-badge {
            margin: 2px;
            font-size: 0.8em;
        }
        
        .action-buttons {
            margin-top: 15px;
            text-align: center;
        }
        
        .stat-box {
            text-align: center;
            padding: 10px;
        }
        
        .stat-box h4 {
            margin: 0;
            font-size: 2em;
        }
        
        .panel-title i {
            margin-right: 8px;
        }
    </style>
</f:section>

</html>
