<?php
return [
    'ctrl' => [
        'title' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports',
        'label' => 'name',
        'label_alt' => 'code',
        'label_alt_force' => true,
        'tstamp' => 'tstamp',
        'crdate' => 'crdate',
        'searchFields' => 'code,name,location_name,country_code,ident',
        'iconfile' => 'EXT:landing-pages/Resources/Public/Icons/tx_landingpages_airports.svg',
        'rootLevel' => 1,
        'security' => [
            'ignorePageTypeRestriction' => true,
        ],
    ],
    'types' => [
        '1' => [
            'showitem' => '
                --palette--;;basic_info,
                --palette--;;location_info,
                --palette--;;geographic_info,
                --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:extended
            '
        ]
    ],
    'palettes' => [
        'basic_info' => [
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.palette.basic_info',
            'showitem' => 'code, ident, --linebreak--, name, location_name',
        ],
        'location_info' => [
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.palette.location_info',
            'showitem' => 'country_code, iso_region, --linebreak--, continent, city_id',
        ],
        'geographic_info' => [
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.palette.geographic_info',
            'showitem' => 'lat, lon, --linebreak--, elevation, ccd',
        ],
    ],
    'columns' => [
        'code' => [
            'exclude' => false,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.code',
            'config' => [
                'type' => 'input',
                'size' => 5,
                'max' => 3,
                'eval' => 'trim,upper,required',
                'placeholder' => 'BER',
            ],
        ],
        'ccd' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.ccd',
            'config' => [
                'type' => 'input',
                'size' => 5,
                'max' => 3,
                'eval' => 'trim,upper',
            ],
        ],
        'ident' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.ident',
            'config' => [
                'type' => 'input',
                'size' => 15,
                'max' => 10,
                'eval' => 'trim,upper',
                'placeholder' => 'EDDB',
            ],
        ],
        'lat' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.lat',
            'config' => [
                'type' => 'input',
                'size' => 15,
                'max' => 20,
                'eval' => 'trim',
                'placeholder' => '52.362247',
            ],
        ],
        'lon' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.lon',
            'config' => [
                'type' => 'input',
                'size' => 15,
                'max' => 20,
                'eval' => 'trim',
                'placeholder' => '13.500672',
            ],
        ],
        'elevation' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.elevation',
            'config' => [
                'type' => 'number',
                'size' => 10,
                'default' => 0,
            ],
        ],
        'continent' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.continent',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'items' => [
                    ['label' => '', 'value' => ''],
                    ['label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.continent.AF', 'value' => 'AF'],
                    ['label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.continent.AN', 'value' => 'AN'],
                    ['label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.continent.AS', 'value' => 'AS'],
                    ['label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.continent.EU', 'value' => 'EU'],
                    ['label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.continent.NA', 'value' => 'NA'],
                    ['label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.continent.OC', 'value' => 'OC'],
                    ['label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.continent.SA', 'value' => 'SA'],
                ],
                'default' => '',
            ],
        ],
        'country_code' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.country_code',
            'config' => [
                'type' => 'input',
                'size' => 5,
                'max' => 2,
                'eval' => 'trim,upper',
                'placeholder' => 'DE',
            ],
        ],
        'iso_region' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.iso_region',
            'config' => [
                'type' => 'input',
                'size' => 10,
                'max' => 6,
                'eval' => 'trim,upper',
                'placeholder' => 'DE-BB',
            ],
        ],
        'location_name' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.location_name',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'max' => 250,
                'eval' => 'trim',
                'placeholder' => 'Berlin',
            ],
        ],
        'name' => [
            'exclude' => false,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.name',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'max' => 250,
                'eval' => 'trim,required',
                'placeholder' => 'Berlin Brandenburg Airport',
            ],
        ],
        'city_id' => [
            'exclude' => true,
            'label' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_db.xlf:tx_landingpages_airports.city_id',
            'config' => [
                'type' => 'number',
                'size' => 10,
                'default' => 0,
            ],
        ],
    ],
];
